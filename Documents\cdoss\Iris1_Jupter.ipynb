import numpy as np
import pandas as pd
import matplotlib as plt
import seaborn as sns
import matplotlib.pyplot as plt
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, confusion_matrix, precision_score, recall_score, classification_report, f1_score
from sklearn import metrics
from sklearn.tree import DecisionTreeClassifier
from sklearn.tree import plot_tree
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import GridSearchCV
import warnings
warnings.filterwarnings('ignore')



df = pd.read_csv('Iris1.csv')

df.info()


#Affichage de description statistique des données catégoriques
df.describe(include=['object'])

#Affichage de description statistique des données numérique
df.describe(include=['number'])

df.shape

df['species'].value_counts()

df.dtypes

df.head()

df.isnull().any()

df.isnull().sum()

df.isna().sum()

numerical = df.select_dtypes(include=['int64', 'float64'])
categorical = df.select_dtypes(include='object')

print("Numerical:\n", numerical.head())
print("Categorical:\n", categorical.head())

plt.figure(figsize=(10, 6))
sns.boxplot( data=df)
plt.show()

Q1, Q3 = df["sepal_width"].quantile([0.25, 0.75])
IQR = Q3 - Q1
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR

# Affichage des outliers détectés
outliers = df[(df["sepal_width"] < lower_bound) | (df["sepal_width"] > upper_bound)]
print("Valeurs aberrantes de sepal_width :")
print(outliers["sepal_width"])

df_median = df.copy()
median_value = df["sepal_width"].median()
df_median.loc[df_median["sepal_width"] < lower_bound, "sepal_width"] = median_value
df_median.loc[df_median["sepal_width"] > upper_bound, "sepal_width"] = median_value

print("\nValeurs après remplacement des outliers par la médiane :")
print(df_median["sepal_width"])


✅ Option 2 : Remplacer les outliers par la moyenne

df_mean = df.copy()
mean_value = df["sepal_width"].mean()
df_mean.loc[df_mean["sepal_width"] < lower_bound, "sepal_width"] = mean_value
df_mean.loc[df_mean["sepal_width"] > upper_bound, "sepal_width"] = mean_value

print("\nValeurs après remplacement des outliers par la moyenne :")
print(df_mean["sepal_width"])

✅ Option 3 : Supprimer les outliers

df_clean = df[(df["sepal_width"] >= lower_bound) & (df["sepal_width"] <= upper_bound)]

print("\nValeurs restantes après suppression des outliers :")
print(df_clean["sepal_width"])
print(f"Nombre de lignes restantes : {df_clean.shape[0]}")

print("Duplicates:", df.duplicated().sum())
df.drop_duplicates(inplace=True)

print("Duplicates:", df.duplicated().sum())

X = df.drop("species", axis=1)
y = df["species"]

from sklearn.preprocessing import LabelEncoder

# Initialiser l'encodeur
label_encoder = LabelEncoder()

# Appliquer l'encodage sur la cible
y = label_encoder.fit_transform(df["species"])

# Affichage des classes encodées
print("Classes :", label_encoder.classes_)
print("y encodé :", y[:5])

# Division de les données en train et test
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
X_train = scaler.fit_transform(X_train)
X_test = scaler.transform(X_test)
X_train[0:5,:]

from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report
from sklearn.neighbors import KNeighborsClassifier

# Entraîner le modèle
model = KNeighborsClassifier(n_neighbors=5)
model.fit(X_train, y_train)

# Prédictions
y_pred = model.predict(X_test)

# Évaluation
accuracy = accuracy_score(y_test, y_pred)
print(f"Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred, average='macro')
print(f"Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred, average='macro')
print(f"Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred, average='macro')
print(f"F1-score: {f1:.2f}")

# Affichage du rapport de classification
print("Classification Report:\n", classification_report(y_test, y_pred, target_names=label_encoder.classes_))

from sklearn.model_selection import GridSearchCV
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

# Définir la grille des hyperparamètres
param_grid = {
    'n_neighbors': [3, 5, 7, 9],                 # Nombre de voisins
    'weights': ['uniform', 'distance'],         # Type de pondération
    'metric': ['euclidean', 'manhattan']        # Distance utilisée
}

# Initialiser le modèle
knn = KNeighborsClassifier()

# Configuration de la Grid Search
grid_search = GridSearchCV(estimator=knn,
                           param_grid=param_grid,
                           scoring='accuracy',
                           cv=5,
                           n_jobs=-1)

# Lancer la recherche sur les données d'entraînement
grid_search.fit(X_train, y_train)

# Afficher les meilleurs hyperparamètres
print("🔍 Meilleurs paramètres :", grid_search.best_params_)

# Meilleur score de validation croisée
print(f"✅ Meilleur score CV : {grid_search.best_score_:.2f}")

# Utiliser le meilleur modèle trouvé
best_knn = grid_search.best_estimator_

# Prédictions sur les données de test
y_pred = best_knn.predict(X_test)

# Évaluation complète
accuracy = accuracy_score(y_test, y_pred)
print(f"\n🎯 Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred, average='macro')
print(f"🎯 Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred, average='macro')
print(f"🎯 Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred, average='macro')
print(f"🎯 F1-score: {f1:.2f}")

# Rapport de classification
print("\n📋 Classification Report :")
print(classification_report(y_test, y_pred, target_names=label_encoder.classes_))


from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

# Initialiser le modèle
dt_model = DecisionTreeClassifier(random_state=42)

# Entraîner
dt_model.fit(X_train, y_train)

# Prédictions
y_pred_dt = dt_model.predict(X_test)

# Évaluation
accuracy = accuracy_score(y_test, y_pred_dt)
print(f"\n🎯 Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred_dt, average='macro')
print(f"🎯 Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred_dt, average='macro')
print(f"🎯 Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred_dt, average='macro')
print(f"🎯 F1-score: {f1:.2f}")

# Rapport de classification
print("\n📋 Classification Report :")
print(classification_report(y_test, y_pred_dt, target_names=label_encoder.classes_))


from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

# Initialiser le modèle Random Forest
rf_model = RandomForestClassifier(random_state=42, n_estimators=100)  # n_estimators = nombre d'arbres

# Entraîner
rf_model.fit(X_train, y_train)

# Prédictions
y_pred_rf = rf_model.predict(X_test)

# Évaluation
accuracy = accuracy_score(y_test, y_pred_rf)
print(f"\n🎯 Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred_rf, average='macro')
print(f"🎯 Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred_rf, average='macro')
print(f"🎯 Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred_rf, average='macro')
print(f"🎯 F1-score: {f1:.2f}")

# Rapport de classification
print("\n📋 Classification Report :")
print(classification_report(y_test, y_pred_rf, target_names=label_encoder.classes_))


from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

# Initialiser le modèle SVM
svm_model = SVC(random_state=42, kernel='rbf')  # kernel peut être 'linear', 'poly', 'rbf' (par défaut)

# Entraîner
svm_model.fit(X_train, y_train)

# Prédictions
y_pred_svm = svm_model.predict(X_test)

# Évaluation
accuracy = accuracy_score(y_test, y_pred_svm)
print(f"\n🎯 Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred_svm, average='macro')
print(f"🎯 Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred_svm, average='macro')
print(f"🎯 Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred_svm, average='macro')
print(f"🎯 F1-score: {f1:.2f}")

# Rapport de classification
print("\n📋 Classification Report :")
print(classification_report(y_test, y_pred_svm, target_names=label_encoder.classes_))

from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

# Initialiser le modèle SVM avec kernel
svm_model = SVC(kernel='rbf', random_state=42)  # Change 'rbf' to 'linear', 'poly', or 'sigmoid' if you want

# Entraîner le modèle
svm_model.fit(X_train, y_train)

# Prédictions
y_pred_svm = svm_model.predict(X_test)

# Évaluation
accuracy = accuracy_score(y_test, y_pred_svm)
print(f"\n🎯 Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred_svm, average='macro')
print(f"🎯 Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred_svm, average='macro')
print(f"🎯 Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred_svm, average='macro')
print(f"🎯 F1-score: {f1:.2f}")

# Rapport de classification
print("\n📋 Classification Report :")
print(classification_report(y_test, y_pred_svm, target_names=label_encoder.classes_))


from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

# Initialiser le modèle Logistic Regression
logreg_model = LogisticRegression(random_state=42, max_iter=1000)  # max_iter augmenté pour assurer convergence

# Entraîner
logreg_model.fit(X_train, y_train)

# Prédictions
y_pred_logreg = logreg_model.predict(X_test)

# Évaluation
accuracy = accuracy_score(y_test, y_pred_logreg)
print(f"\n🎯 Accuracy: {accuracy:.2f}")

precision = precision_score(y_test, y_pred_logreg, average='macro')
print(f"🎯 Precision: {precision:.2f}")

recall = recall_score(y_test, y_pred_logreg, average='macro')
print(f"🎯 Recall: {recall:.2f}")

f1 = f1_score(y_test, y_pred_logreg, average='macro')
print(f"🎯 F1-score: {f1:.2f}")

# Rapport de classification
print("\n📋 Classification Report :")
print(classification_report(y_test, y_pred_logreg, target_names=label_encoder.classes_))


y_scores = LR.predict_proba(X_test_sc)[:, 1]

# Calcul de la courbe ROC
fpr, tpr, thresholds = roc_curve(y_test, y_scores)
roc_auc = auc(fpr, tpr)

# Tracé de la courbe ROC
plt.figure(figsize=(8, 6))
plt.plot(fpr, tpr, color='darkgreen', lw=2, label="AUC = {:.3f}".format(roc_auc))
plt.plot([0, 1], [0, 1], color="grey", linestyle="--")
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel("Taux de Faux Positifs (FPR)")
plt.ylabel("Taux de Vrais Positifs (TPR)")
plt.title("Courbe ROC - Régression Logistique")
plt.legend(loc="lower right")
plt.show()

import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc

# Plot ROC curves for each model
plt.figure(figsize=(8, 6))

# Random Forest
fpr_rf, tpr_rf, _ = roc_curve(y_test, rf.predict_proba(X_test_sc)[:, 1])
plt.plot(fpr_rf, tpr_rf, label="Random Forest (AUC = {:.3f})".format(auc(fpr_rf, tpr_rf)))

# Linear SVM
fpr_svm_l, tpr_svm_l, _ = roc_curve(y_test, linear_SVM.decision_function(X_test_sc))
plt.plot(fpr_svm_l, tpr_svm_l, label="Linear SVM (AUC = {:.3f})".format(auc(fpr_svm_l, tpr_svm_l)))

# RBF SVM
fpr_svm_k, tpr_svm_k, _ = roc_curve(y_test, kernel_SVM.decision_function(X_test_sc))
plt.plot(fpr_svm_k, tpr_svm_k, label="RBF SVM (AUC = {:.3f})".format(auc(fpr_svm_k, tpr_svm_k)))

# Logistic Regression
fpr_lr, tpr_lr, _ = roc_curve(y_test, LR.predict_proba(X_test_sc)[:, 1])
plt.plot(fpr_lr, tpr_lr, label="Logistic Regression (AUC = {:.3f})".format(auc(fpr_lr, tpr_lr)))

# KNN
fpr_knn, tpr_knn, _ = roc_curve(y_test, knn.predict_proba(X_test_sc)[:, 1])
plt.plot(fpr_knn, tpr_knn, label="KNN (AUC = {:.3f})".format(auc(fpr_knn, tpr_knn)))

# Grille Model (Assuming it's a KNN model)
fpr_grille, tpr_grille, _ = roc_curve(y_test, grille.predict_proba(X_test_sc)[:, 1])
plt.plot(fpr_grille, tpr_grille, label="Grille Model (AUC = {:.3f})".format(auc(fpr_grille, tpr_grille)))

# Formatting the plot
plt.plot([0, 1], [0, 1], color="grey", linestyle="--")
plt.xlabel("False Positive Rate (FPR)")
plt.ylabel("True Positive Rate (TPR)")
plt.title("Comparaison des courbes ROC")
plt.legend(loc="lower right")
plt.show()


